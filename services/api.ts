export const TMDB_CONFIG = {
	API_KEY: process.env.EXPO_PUBLIC_MOVIE_API_KEY,
	BASE_URL: 'https://api.themoviedb.org/3',
	Headers: {
		// 'Content-Type': 'application/json',
		accept: 'application/json',
		Authorization: `Bearer ${process.env.EXPO_PUBLIC_MOVIE_API_KEY}`,
	},
};
export const fetchMovies = async ({
	query,
}: {
	query: string;
}): Promise<Movie[]> => {
	const endpoint = query
		? `${TMDB_CONFIG.BASE_URL}/search/movie?query=${encodeURIComponent(query)}`
		: `${TMDB_CONFIG.BASE_URL}/discover/movie?sort_by=popularity.desc`;
	const response = await fetch(endpoint, {
		method: 'GET',
		headers: TMDB_CONFIG.Headers,
	});

	if (!response.ok) {
		throw new Error(`Failed to fetch movies: ${response.statusText}`);
	}

	const data = await response.json();
	// console.log(data);

	return data.results;
};
export const fetchMovieDetails = async (
	movieId: string,
): Promise<MovieDetails> => {
	try {
		const response = await fetch(
			`${TMDB_CONFIG.BASE_URL}/movie/${movieId}?api_key=${TMDB_CONFIG.API_KEY}`,
			{
				method: 'GET',
				headers: TMDB_CONFIG.Headers,
			},
		);

		if (!response.ok) {
			throw new Error(`Failed to fetch movie details: ${response.statusText}`);
		}

		const data = await response.json();
		return data;
	} catch (error) {
		console.error('Error fetching movie details:', error);
		throw error;
	}
};
