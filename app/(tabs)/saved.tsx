import { images } from '@/constants/images';
import { useRouter } from 'expo-router';
import React from 'react';
import { FlatList, Image, Text, TouchableOpacity, View } from 'react-native';

const mockSaved = [
	// Sci-Fi Movies
	{
		id: '1',
		title: 'Inception',
		category: 'Sci-Fi',
		poster_path: '/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg',
		vote_average: 8.8,
		release_date: '2010-07-16',
	},
	{
		id: '2',
		title: 'The Matrix',
		category: 'Sci-Fi',
		poster_path: '/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg',
		vote_average: 8.7,
		release_date: '1999-03-30',
	},
	// ... Add similar data for other movies
];

const SavedMovieCard = ({ movie }) => {
	const router = useRouter();
	const imageUrl = `https://image.tmdb.org/t/p/w500${movie.poster_path}`;

	return (
		<TouchableOpacity
			className='mb-4 bg-[#ffffff10] rounded-xl overflow-hidden'
			onPress={() => router.push(`/movie/${movie.id}`)}>
			<View className='flex-row h-32'>
				<Image
					source={{ uri: imageUrl }}
					className='w-24 h-full rounded-l-xl'
					resizeMode='cover'
				/>
				<View className='flex-1 p-3 justify-between'>
					<View>
						<Text
							className='text-white text-lg font-semibold mb-1'
							numberOfLines={1}>
							{movie.title}
						</Text>
						<Text className='text-gray-400 text-sm'>{movie.category}</Text>
					</View>
					<View className='flex-row justify-between items-center'>
						<Text className='text-gray-400 text-sm'>
							{new Date(movie.release_date).getFullYear()}
						</Text>
						<View className='bg-[#ffffff20] px-2 py-1 rounded-full'>
							<Text className='text-white text-sm'>
								⭐ {movie.vote_average.toFixed(1)}
							</Text>
						</View>
					</View>
				</View>
			</View>
		</TouchableOpacity>
	);
};

const Saved = () => {
	return (
		<View className='flex-1 bg-primary'>
			<Image
				source={images.bg}
				className='absolute w-full h-full'
			/>
			<View className='flex-1 pt-14'>
				<View className='px-4 flex-row items-center justify-between mb-6'>
					<Text className='text-2xl font-bold text-white'>Saved Movies</Text>
					<Text className='text-gray-400'>{mockSaved.length} movies</Text>
				</View>

				<FlatList
					data={mockSaved}
					keyExtractor={(item) => item.id}
					contentContainerStyle={{ paddingHorizontal: 16 }}
					showsVerticalScrollIndicator={false}
					renderItem={({ item }) => <SavedMovieCard movie={item} />}
					ListEmptyComponent={
						<View className='flex-1 justify-center items-center mt-20'>
							<Text className='text-gray-400 text-lg'>No saved movies yet</Text>
							<TouchableOpacity
								className='mt-4 bg-blue-500 px-6 py-3 rounded-full'
								onPress={() => router.push('/')}>
								<Text className='text-white font-semibold'>Browse Movies</Text>
							</TouchableOpacity>
						</View>
					}
				/>
			</View>
		</View>
	);
};

export default Saved;
